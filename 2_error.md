> Task :app:compileDebugKotlin FAILED
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/details/components/ScoreChart.kt:329:30 None of the following candidates is applicable:
@Stable() fun Modifier.height(intrinsicSize: IntrinsicSize): Modifier
@Stable() fun Modifier.height(height: Dp): Modifier
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/details/components/ScoreChart.kt:329:39 None of the following candidates is applicable:
@InlineOnly() fun BigDecimal.div(other: BigDecimal): BigDecimal
@InlineOnly() fun BigInteger.div(other: BigInteger): BigInteger
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/details/components/ScoreChart.kt:333:30 Overload resolution ambiguity between candidates:
@IntrinsicConstEvaluation() fun plus(other: Byte): Float
@IntrinsicConstEvaluation() fun plus(other: Double): Double
@IntrinsicConstEvaluation() fun plus(other: Float): Float
@IntrinsicConstEvaluation() fun plus(other: Int): Float
@IntrinsicConstEvaluation() fun plus(other: Long): Float
@IntrinsicConstEvaluation() fun plus(other: Short): Float
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/details/components/ScoreChart.kt:333:34 Overload resolution ambiguity between candidates:
@IntrinsicConstEvaluation() fun times(other: Byte): Int
@IntrinsicConstEvaluation() fun times(other: Double): Double
@IntrinsicConstEvaluation() fun times(other: Float): Float
@IntrinsicConstEvaluation() fun times(other: Int): Int
@IntrinsicConstEvaluation() fun times(other: Long): Long
@IntrinsicConstEvaluation() fun times(other: Short): Int
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/details/components/ScoreChart.kt:350:23 Overload resolution ambiguity between candidates:
fun String?.plus(other: Any?): String
fun <T> Array<T>.plus(element: T): Array<T>
fun FloatArray.plus(element: Float): FloatArray
fun <T> Collection<T>.plus(element: T): List<T>
fun <T> Iterable<T>.plus(element: T): List<T>
fun <T> Set<T>.plus(element: T): Set<T>
fun <T> Sequence<T>.plus(element: T): Sequence<T>

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':app:compileDebugKotlin'.
> A failure occurred while executing org.jetbrains.kotlin.compilerRunner.GradleCompilerRunnerWithWorkers$GradleKotlinCompilerWorkAction
   > Compilation error. See log for more details

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

BUILD FAILED in 3s
30 actionable tasks: 2 executed, 28 up-to-date